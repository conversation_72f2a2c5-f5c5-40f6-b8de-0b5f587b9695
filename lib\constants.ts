export const MODELS = [
  {
    label: "DeepSeek V3",
    value: "deepseek-ai/DeepSeek-V3",
  },
  {
    label: "Kimi K2",
    value: "moonshotai/Kimi-K2-Instruct",
  },
  {
    label: "Qwen 2.5 Coder 32B",
    value: "Qwen/Qwen2.5-Coder-32B-Instruct",
  },
  {
    label: "Llama 3.3 70B",
    value: "meta-llama/Llama-3.3-70B-Instruct-Turbo",
  },
];

export const SUGGESTED_PROMPTS = [
  {
    title: "Quiz app",
    description:
      "Make me a quiz app about American history. Make sure to give the user an explanation on each question whether they got it right or wrong and keep a score going",
  },
  {
    title: "SaaS Landing page",
    description:
      "A landing page for a SaaS business that includes a clear value proposition in a prominent hero section, concise feature overviews, testimonials, pricing, and a clear call-to-action button leading to a free trial or demo.",
  },
  {
    title: "Pomodoro Timer",
    description:
      "Make a beautiful pomodoro timer where I can adjust the lengths of the focus time and the break and it will beep when done.",
  },
  {
    title: "Blog app",
    description:
      "Make me a blog app that has a few blogs there for people to read. Users can click into the blogs and read them, then go back to the homepage to see more.",
  },
  // {
  //   title: "Recipe site",
  //   description:
  //     "Make me a site that has easy to make recipes in a grid that you can click into and see the full recipe. Also make it possible for me to add my own",
  // },
  {
    title: "Flashcard app",
    description:
      "Build me a flashcard app about llamas. Have some flash cards and also have the ability for users to add their own. Show one side of a card at first and reveal the answer on button click, keeping track of correct guesses to measure progress.",
  },
  {
    title: "Timezone dashboard",
    description:
      "Make me a time zone dashboard that shows me the time zone in the top 6 most popular time zones and gives me a dropdown to add others",
  },
];
