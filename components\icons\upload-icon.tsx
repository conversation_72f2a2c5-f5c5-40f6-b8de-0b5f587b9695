import { ComponentProps } from "react";

export default function UploadIcon(props: ComponentProps<"svg">) {
  return (
    <svg
      height={10}
      width={10}
      viewBox="0 0 8.4666664 8.4666664"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M5.465 289.749c-.91-.019-1.849.503-2.106 1.678-1.048-.357-1.994.348-1.896 1.475a1.493 1.493 0 00-.934 1.387c0 .829.668 1.496 1.497 1.496h1.818l-.002-1.706-.426.155c-.31.133-.538-.257-.273-.5l.807-.783c.184-.184.404-.163.567 0l.807.784c.265.242.036.632-.273.499l-.426-.155v1.706H6.44c.829 0 1.496-.667 1.496-1.496 0-.61-.362-1.132-.883-1.365 1.01-1.97-.259-3.149-1.589-3.175z"
        fill="#fff"
        transform="translate(0 -288.533)"
      />
      <path
        d="M5.465 289.748c-.05 0-.106.001-.157.003-1.162 1.832.634 3.172 1.746 3.172 1.01-1.97-.259-3.148-1.589-3.175z"
        fill="#fff"
        transform="translate(0 -288.533)"
      />
    </svg>
  );
}
